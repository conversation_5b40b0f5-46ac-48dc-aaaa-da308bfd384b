[
  {
    "text": "restoredb snapshot global.teardown.ts",
    "intent": "test_skipped",
    "response": "Test 'do restoredb snapshot' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "setup global.setup.ts",
    "intent": "general_testing",
    "response": "This is a general test case. Test: 'Setup' validates application functionality."
  },
  {
    "text": "trigger error snackbar broken bulk api requests api error notifications",
    "intent": "api_testing",
    "response": "This involves API testing. Test: 'can trigger an error snackbar for broken bulk api requests' validates API behavior and responses."
  },
  {
    "text": "trigger error snackbar multiple broken bulk api requests api error notifications",
    "intent": "api_testing",
    "response": "This involves API testing. Test: 'can trigger an error snackbar with multiple broken bulk api requests' validates API behavior and responses."
  },
  {
    "text": "card details share link button visible clickable sprint execution Asset Details asset details cards",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Card Details Share Link button is visible and clickable' validates execution features."
  },
  {
    "text": "card details workitem deleted sprint execution Asset Details asset details cards",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Card Details Workitem can be deleted' validates execution features."
  },
  {
    "text": "permission card details share link quick close delete buttons not visible clickable sprint execution Asset Details asset details cards",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Permission: Card Details,  Share Link / Quick Close / Delete buttons are NOT visible and clickable' validates execution features."
  },
  {
    "text": "asset details display correct values sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'asset details display correct values' validates execution features."
  },
  {
    "text": "add comment delete sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'add comment and delete it' validates execution features."
  },
  {
    "text": "assetdetails add owner sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: Add owner' validates execution features."
  },
  {
    "text": "previewing comment sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Previewing a  comment' validates execution features."
  },
  {
    "text": "add comment mention sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'add comment with mention' validates execution features."
  },
  {
    "text": "assetdetails show more less sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: show more/less' validates execution features."
  },
  {
    "text": "add comment reply sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'add comment and reply it' validates execution features."
  },
  {
    "text": "assetdetails change status sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: change status' validates execution features."
  },
  {
    "text": "assetdetails change estimate sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: change estimate' validates execution features."
  },
  {
    "text": "assetdetails change title long title sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: change title/long title' validates execution features."
  },
  {
    "text": "assetdetails add filter edit delete link sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: add/filter/edit/delete Link' validates execution features."
  },
  {
    "text": "assetdetails cancel using esc when adding new link sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: cancel using ESC when adding a new Link' validates execution features."
  },
  {
    "text": "assetdetails add link disable invalid url sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: add Link disable with invalid URL' validates execution features."
  },
  {
    "text": "assetdetails add comment clickable url delete sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'AssetDetails: add comment with clickable url and delete it' validates execution features."
  },
  {
    "text": "asset details check scope project planninglevel hierarchy descendants appearing below indented sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Check Scope/Project/PlanningLevel hierarchy for descendants appearing 'below' and 'indented'' validates execution features."
  },
  {
    "text": "creating task card details displays progress status sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Creating a Task in card details displays a In Progress Status' validates execution features."
  },
  {
    "text": "creating task card details displays completed status sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Creating a Task in card details displays a Completed Status' validates execution features."
  },
  {
    "text": "create new story story name should correctly display sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'If I create a new story, the story name should correctly display' validates execution features."
  },
  {
    "text": "quick close click ellipses story choose quick close story should not available sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Quick Close: If I click the ellipses on a story and choose quick close, the story should not be available' validates execution features."
  },
  {
    "text": "expand collapse asset details drawer sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Expand/Collapse Asset Details Drawer' validates execution features."
  },
  {
    "text": "quick close quick close menu item visible closes workitem using keyboard sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Quick Close: Quick Close menu item is visible and closes the workitem using the keyboard' validates execution features."
  },
  {
    "text": "title text should not visble card detail when title card clicked sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: ''Title' text should not be visble on card detail when the Title of the card is clicked' validates execution features."
  },
  {
    "text": "permission error message when permission assign scope sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Permission: Error message when no permission to assign to scope' validates execution features."
  },
  {
    "text": "asset details information section displays proper fields sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Information section displays proper fields' validates execution features."
  },
  {
    "text": "asset details copy workitem spinner options menu sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Copy workitem/Spinner in options menu' validates execution features."
  },
  {
    "text": "asset details work item title edit mode exits when using esc key keyboard sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Work Item Title edit mode exits when using the esc key on the keyboard' validates execution features."
  },
  {
    "text": "asset details work item title edit mode saves title when using enter key keyboard sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Work Item Title edit mode saves title when using the Enter key on the keyboard' validates execution features."
  },
  {
    "text": "asset details work item title saved when edit mode clicking check icon sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Work Item Title is saved when in edit mode and clicking the check icon' validates execution features."
  },
  {
    "text": "asset details work item title cancels edit mode when clicking icon sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Work Item Title cancels edit mode when clicking the X icon' validates execution features."
  },
  {
    "text": "asset details share link icon copies correct link clipboard sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Share link icon copies the correct link to the clipboard' validates execution features."
  },
  {
    "text": "asset details more menu configure fields option defaults project field enabled sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: More Menu Configure Fields Option defaults the Project field to be enabled' validates execution features."
  },
  {
    "text": "asset details more menu configure fields option hides displays owners field sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: More Menu Configure Fields Option hides and displays the Owners field' validates execution features."
  },
  {
    "text": "asset details more menu configure fields option cancels changes when user clicks cancel button sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: More Menu Configure Fields Option cancels changes when the user clicks the Cancel button' validates execution features."
  },
  {
    "text": "asset details project scope selector successfully changed different scope sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Project/Scope selector can successfully be changed to a different scope' validates execution features."
  },
  {
    "text": "asset details portfolio item field edited selection visible sprint execution Asset Details asset details drawer",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details: Portfolio Item field can be edited and the selection is visible' validates execution features."
  },
  {
    "text": "asset details relations add filter edit delete link sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: add/filter/edit/delete Link' validates execution features."
  },
  {
    "text": "asset details relations cancel using esc when adding new link sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: cancel using ESC when adding a new Link' validates execution features."
  },
  {
    "text": "asset details relations add link disable invalid url sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: add Link disable with invalid URL' validates execution features."
  },
  {
    "text": "asset details relations add task sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: add Task' validates execution features."
  },
  {
    "text": "asset details relations task filter sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: Task filter' validates execution features."
  },
  {
    "text": "asset details relations modify existing task sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: modify existing Task' validates execution features."
  },
  {
    "text": "asset details relations relation tooltip should display name count sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: Relation tooltip should display name and count' validates execution features."
  },
  {
    "text": "asset details relations attachments crud sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: Attachments CRUD' validates execution features."
  },
  {
    "text": "asset details relations creating task card details displays progress status sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: Creating a Task in card details displays a In Progress Status' validates execution features."
  },
  {
    "text": "asset details relations creating task card details displays completed status sprint execution Asset Details asset details relations",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Asset Details Relations: Creating a Task in card details displays a Completed Status' validates execution features."
  },
  {
    "text": "storyboard taskboard have different filter options sprint execution board board filters",
    "intent": "test_skipped",
    "response": "Test 'Storyboard/Taskboard have different filter options' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "should filter board member clear filter sprint execution board board filters",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'should filter board by member and clear filter' validates execution features."
  },
  {
    "text": "should show tooltip email when filtering owner sprint execution board board filters",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'should show tooltip with email when filtering by owner' validates execution features."
  },
  {
    "text": "should filter board multiple members sprint execution board board filters",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'should filter board by multiple members, ' validates execution features."
  },
  {
    "text": "39274 removing workitem owner does not unfilter board sprint execution board board filters",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'D-39274: Removing workitem owner does not unfilter board' validates execution features."
  },
  {
    "text": "board filter filter tag sprint execution board board filters",
    "intent": "test_skipped",
    "response": "Test 'Board Filter: Filter by Tag' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board auto collapse empty columns toggle works when filtering owner sprint execution board board filters",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Board Auto collapse empty columns toggle works when filtering by owner' validates execution features."
  },
  {
    "text": "board filter filter portfolio item sprint execution board board filters",
    "intent": "test_skipped",
    "response": "Test 'Board Filter: Filter by Portfolio Item' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board filter filter estimate sprint execution board board filters",
    "intent": "test_skipped",
    "response": "Test 'Board Filter: Filter by Estimate' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board filter clicking board chips removes filters sprint execution board board filters",
    "intent": "test_skipped",
    "response": "Test 'Board Filter: clicking board chips removes filters' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "permission convert story defect button not visible sprint execution board board permissions",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'No permission. Convert 'Story to Defect' button not visible' validates execution features."
  },
  {
    "text": "board swimlanes group priority drag between priorities sprint execution board board swimlanes contained",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Board Swimlanes: Group by Priority, Drag between priorities' validates execution features."
  },
  {
    "text": "board swimlanes group story type change story type get new swimlane sprint execution board board swimlanes",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Board Swimlanes: Group by story type, change story type to get new swimlane' validates execution features."
  },
  {
    "text": "board swimlanes group defect type sprint execution board board swimlanes",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Board Swimlanes: Group by Defect Type' validates execution features."
  },
  {
    "text": "iteration instead timebox sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Iteration instead of timebox' validates execution features."
  },
  {
    "text": "left side nav highlight shows highlighted sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Left side nav highlight shows SE highlighted' validates execution features."
  },
  {
    "text": "cancel adding story defect board sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Cancel adding story/defect to board' validates execution features."
  },
  {
    "text": "add story different sprint than current sprint sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Add story with different sprint than current sprint' validates execution features."
  },
  {
    "text": "workitems should pre populated iteration scope from board sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Workitems should be pre-populated with the iteration/scope from the board' validates execution features."
  },
  {
    "text": "cards relation tooltip should display name count sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Cards: Relation tooltip should display name and count' validates execution features."
  },
  {
    "text": "drag drop cards sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Drag 'N' Drop Cards' validates execution features."
  },
  {
    "text": "drag drop cards into collapsed column sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Drag 'N' Drop Cards into collapsed Column' validates execution features."
  },
  {
    "text": "cards pressing enter key card focused opens asset details sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Cards: Pressing enter key with a card focused opens asset details' validates execution features."
  },
  {
    "text": "cards upstream downstream dependencies indicator sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Cards: Upstream/Downstream dependencies indicator' validates execution features."
  },
  {
    "text": "filter board member sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Filter board by member' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "clicking attachment link card takes you attachment side nav sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Clicking Attachment link on Card takes you to the attachment side nav' validates execution features."
  },
  {
    "text": "click ellipses story there should options visible sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'If I click the ellipses on a story, there should be options visible' validates execution features."
  },
  {
    "text": "click ellipses story choose edit option story should open view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'If I click the ellipses on a story and choose the edit option, the story should open to view' validates execution features."
  },
  {
    "text": "quick close quick close asset from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Quick Close: Quick Close Asset from the Board view' validates execution features."
  },
  {
    "text": "convert story defect story sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Convert Story to Defect to Story' validates execution features."
  },
  {
    "text": "burndown chart configured sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Burndown chart can be configured' validates execution features."
  },
  {
    "text": "card background color matches status column header color bar sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Card background color matches status column header color bar' validates execution features."
  },
  {
    "text": "card hover should display proper menu items sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Card Hover should display proper menu items' validates execution features."
  },
  {
    "text": "copy card from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Copy Card from the Board view' validates execution features."
  },
  {
    "text": "move card top from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Move Card to Top from Board View' validates execution features."
  },
  {
    "text": "delete card from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Delete Card from Board View' validates execution features."
  },
  {
    "text": "add test from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Add Test from Board View' validates execution features."
  },
  {
    "text": "add task from board view sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Add Task from Board View' validates execution features."
  },
  {
    "text": "filter iteration buttons not trigger collapsed column expand sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Filter & Iteration buttons do not trigger a collapsed column to expand' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "scrollbars only visible hover sprint execution board board",
    "intent": "sprint_execution",
    "response": "This concerns sprint execution and board functionality. Test: 'Scrollbars only visible on hover' validates execution features."
  },
  {
    "text": "39274 removing workitem owner does not unfilter board sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'D-39274: Removing workitem owner does not unfilter board' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board auto collapse empty columns toggle successfully collapses expands empty columns sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Board Auto collapse empty columns toggle successfully collapses and expands empty columns' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board auto collapse empty columns toggle enables same toggle standup sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Board Auto collapse empty columns toggle enables the same toggle in Standup' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board auto collapse empty columns toggle works when filtering owner sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Board Auto collapse empty columns toggle works when filtering by owner' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board empty columns collapse when owner filter applied additional board filter sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Board empty columns collapse when an owner filter is applied with an additional board filter' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "long statuses truncated sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Long statuses are truncated' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "board long card title wrapped sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'Board: Long card title is wrapped' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "autocollapsed column manually collapsed expanded sprint execution board board",
    "intent": "test_skipped",
    "response": "Test 'AutoCollapsed column can be manually collapsed and expanded' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "kanban room displayed sprint execution board kanban board",
    "intent": "test_skipped",
    "response": "Test 'Kanban room is displayed' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "kanban room backlog column sprint execution board kanban board",
    "intent": "test_skipped",
    "response": "Test 'Kanban room backlog column' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "task board kanban flow sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Task Board in Kanban Flow' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "click ellipses task there should options visible sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'If I click the ellipses on a task, there should be options visible' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "task card side drawer validation sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Task Card side drawer validation' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "click ellipses task choose edit option task should open view sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'If I click the ellipses on a Task and choose the edit option, the task should open to view' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "sign from board view sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'sign me up from Board View' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "delete card from board view sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Delete Card from Board View' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "quick close task from board view sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Quick Close Task from Board View' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "move card top task from board view sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Move Card to Top Task from Board View' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "configure fields card should display checked field sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Configure Fields in Card should display the checked field' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "quick close side drawer should close task sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Quick Close in the side drawer should close the task' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "delete side drawer should delete task sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'Delete in the side drawer should delete the task' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "40851 sprint execution board kanban Taskboard",
    "intent": "test_skipped",
    "response": "Test 'D-40851' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "coreappteam sprint execution configuration",
    "intent": "test_skipped",
    "response": "Test 'coreAppTeam' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "different scope schedule scope scopelabels scopes sprint execution configuration",
    "intent": "test_skipped",
    "response": "Test 'different scope & schedule, no scope.scopelabels.scopes' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "fields find asset epic via number sprint execution fields",
    "intent": "test_skipped",
    "response": "Test 'Fields: can find Asset (Epic) via Number' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "fields add new tag sprint execution fields",
    "intent": "test_skipped",
    "response": "Test 'Fields: can add a new tag' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "fields add remove existing tag sprint execution fields",
    "intent": "test_skipped",
    "response": "Test 'Fields: can add and remove an existing tag' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "live updates topic create move sprint retro sprint execution LiveUpdates live updates",
    "intent": "test_skipped",
    "response": "Test 'Live updates Topic CREATE/MOVE on sprint retro' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "richtext save cancel functionality sprint execution richtext",
    "intent": "test_skipped",
    "response": "Test 'RichText: save and cancel functionality' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "richtext image upload functionality sprint execution richtext",
    "intent": "test_skipped",
    "response": "Test 'RichText: image upload functionality' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "team process statuses displayed sprint execution specific team configurations",
    "intent": "test_skipped",
    "response": "Test 'Team process statuses are displayed' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "teamroom team set none displays sprint execution specific team configurations",
    "intent": "test_skipped",
    "response": "Test 'Teamroom with team set to None displays' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "does standup sprint execution standup tool",
    "intent": "test_skipped",
    "response": "Test 'does standup' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "change iteration during standup sprint execution standup tool",
    "intent": "test_skipped",
    "response": "Test 'can change iteration during standup' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "checked state auto collapse checkbox collapses empty column when checked sprint execution standup tool",
    "intent": "test_skipped",
    "response": "Test 'checked state - Auto collapse checkbox collapses empty column when checked' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "unchecked state auto collapse box unchecked should uncollapse all columns sprint execution standup tool",
    "intent": "test_skipped",
    "response": "Test 'Unchecked State - Auto collapse box unchecked should uncollapse all columns' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "clear standup should uncollapse all collapsed columns sprint execution standup tool",
    "intent": "test_skipped",
    "response": "Test 'Clear Standup should uncollapse all collapsed columns' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "sprint planning view should load sprint planning sprint planning self contained",
    "intent": "test_skipped",
    "response": "Test 'sprint planning view should load' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "left side nav highlight shows highlighted sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Left side nav highlight shows SP highlighted' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "sprint point totals correct sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Sprint point totals are correct' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add story sprint sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Add Story to Sprint' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add defect sprint sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Add Defect to Sprint' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "change sprint assignment sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Change sprint assignment' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add new sprint sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Add New Sprint' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add new sprint naughty name naughty string sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Add New Sprint with naughty name (naughty string)' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add weird sprint should not crash sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Add Weird Sprint should not crash' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "drag drop cards sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Drag 'N' Drop Cards' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "search backlog sprint planning sprint planning",
    "intent": "test_skipped",
    "response": "Test 'Search backlog' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add retrospective kanban teamroom sprint retrospective kanban retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can add a Retrospective in a Kanban Teamroom' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "edit retrospective sprint retrospective kanban retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can edit a Retrospective' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "cancel editing retrospective sprint retrospective kanban retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can cancel editing a Retrospective' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "when adding new retrospective new column box removed after adding column sprint retrospective kanban retrospective",
    "intent": "test_skipped",
    "response": "Test 'When Adding a New Retrospective, the new column box is removed after adding a column' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "when adding new retrospective new column box removed after adding column sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'When Adding a New Retrospective, the new column box is removed after adding a column' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "edit retrospective sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can edit a Retrospective' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "cancel editing retrospective sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can cancel editing a Retrospective' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "add retrospective idea sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can add a Retrospective Idea' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "cancel adding retrospective idea sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can cancel adding a Retrospective Idea' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "edit retrospective idea sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can edit a Retrospective Idea' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "delete retrospective idea sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can delete a Retrospective Idea' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "retrospective opening retro immediately shows data without refresh sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'Retrospective: Opening a retro immediately shows data without refresh' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "drag drop topic into another column sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'I can drag and drop a topic into another column' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "retro board shows timer sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'The retro board shows a timer' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "when multiple retrospectives added only one set columns present sprint retrospective sprint retrospective",
    "intent": "test_skipped",
    "response": "Test 'When multiple retrospectives added, only one set of columns is present' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "delete add new layout user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'Can delete and add a new layout' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "user cancel adding widget user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'User can cancel adding a widget' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "burndown chart user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'burndown chart' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "velocity widget user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'Velocity widget' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "closed items widget user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'Closed Items widget' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "team room user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'team room' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "edit layout name user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'edit layout name' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "multiple widgets added user dashboard layout",
    "intent": "test_skipped",
    "response": "Test 'Multiple Widgets can be added' was skipped. This test may be disabled or conditionally skipped."
  },
  {
    "text": "how many tests passed",
    "intent": "test_count_passed",
    "response": "I can analyze your test results to count passed tests."
  },
  {
    "text": "how many tests passed in last run",
    "intent": "test_count_passed",
    "response": "I can analyze your test results to count passed tests."
  },
  {
    "text": "how many tests passed from test results",
    "intent": "test_count_passed",
    "response": "I can analyze your test results to count passed tests."
  },
  {
    "text": "how many tests passed in current suite",
    "intent": "test_count_passed",
    "response": "I can analyze your test results to count passed tests."
  },
  {
    "text": "how many tests failed",
    "intent": "test_count_failed",
    "response": "I can analyze your test results to count failed tests."
  },
  {
    "text": "how many tests failed in last run",
    "intent": "test_count_failed",
    "response": "I can analyze your test results to count failed tests."
  },
  {
    "text": "how many tests failed from test results",
    "intent": "test_count_failed",
    "response": "I can analyze your test results to count failed tests."
  },
  {
    "text": "how many tests failed in current suite",
    "intent": "test_count_failed",
    "response": "I can analyze your test results to count failed tests."
  },
  {
    "text": "how many tests were skipped",
    "intent": "test_count_skipped",
    "response": "I can analyze your test results to count skipped tests."
  },
  {
    "text": "how many tests were skipped in last run",
    "intent": "test_count_skipped",
    "response": "I can analyze your test results to count skipped tests."
  },
  {
    "text": "how many tests were skipped from test results",
    "intent": "test_count_skipped",
    "response": "I can analyze your test results to count skipped tests."
  },
  {
    "text": "how many tests were skipped in current suite",
    "intent": "test_count_skipped",
    "response": "I can analyze your test results to count skipped tests."
  },
  {
    "text": "total number of tests",
    "intent": "test_count_total",
    "response": "I can count the total number of tests in your test suite."
  },
  {
    "text": "how many total number of tests",
    "intent": "test_count_total",
    "response": "I can count the total number of tests in your test suite."
  },
  {
    "text": "what are the total number of tests",
    "intent": "test_count_total",
    "response": "I can count the total number of tests in your test suite."
  },
  {
    "text": "show me total number of tests",
    "intent": "test_count_total",
    "response": "I can count the total number of tests in your test suite."
  },
  {
    "text": "test execution summary",
    "intent": "test_summary",
    "response": "I can provide a complete summary of your test execution results."
  },
  {
    "text": "how many test execution summary",
    "intent": "test_summary",
    "response": "I can provide a complete summary of your test execution results."
  },
  {
    "text": "what are the test execution summary",
    "intent": "test_summary",
    "response": "I can provide a complete summary of your test execution results."
  },
  {
    "text": "show me test execution summary",
    "intent": "test_summary",
    "response": "I can provide a complete summary of your test execution results."
  },
  {
    "text": "pass rate",
    "intent": "test_pass_rate",
    "response": "I can calculate the test pass rate from your results."
  },
  {
    "text": "how many pass rate",
    "intent": "test_pass_rate",
    "response": "I can calculate the test pass rate from your results."
  },
  {
    "text": "what are the pass rate",
    "intent": "test_pass_rate",
    "response": "I can calculate the test pass rate from your results."
  },
  {
    "text": "show me pass rate",
    "intent": "test_pass_rate",
    "response": "I can calculate the test pass rate from your results."
  },
  {
    "text": "test statistics",
    "intent": "test_statistics",
    "response": "I can provide detailed statistics about your test execution."
  },
  {
    "text": "how many test statistics",
    "intent": "test_statistics",
    "response": "I can provide detailed statistics about your test execution."
  },
  {
    "text": "what are the test statistics",
    "intent": "test_statistics",
    "response": "I can provide detailed statistics about your test execution."
  },
  {
    "text": "show me test statistics",
    "intent": "test_statistics",
    "response": "I can provide detailed statistics about your test execution."
  },
  {
    "text": "overall test status",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many overall test status",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the overall test status",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me overall test status",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "give me a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many give me a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the give me a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me give me a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me a test overview in last run",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me a test overview from test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me a test overview in current suite",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "can you provide a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many can you provide a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the can you provide a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me can you provide a test overview",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "summary of all test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many summary of all test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the summary of all test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me summary of all test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "overall test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many overall test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the overall test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me overall test results",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "overall test execution",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "how many overall test execution",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "what are the overall test execution",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "show me overall test execution",
    "intent": "test_overview",
    "response": "I can give you an overview of your overall test execution status."
  },
  {
    "text": "list flaky tests",
    "intent": "flaky_tests",
    "response": "I can identify potentially flaky tests that have inconsistent results."
  },
  {
    "text": "list flaky tests in last run",
    "intent": "flaky_tests",
    "response": "I can identify potentially flaky tests that have inconsistent results."
  },
  {
    "text": "list flaky tests from test results",
    "intent": "flaky_tests",
    "response": "I can identify potentially flaky tests that have inconsistent results."
  },
  {
    "text": "list flaky tests in current suite",
    "intent": "flaky_tests",
    "response": "I can identify potentially flaky tests that have inconsistent results."
  },
  {
    "text": "show unstable tests",
    "intent": "flaky_tests",
    "response": "I can show you tests that appear to be unstable or flaky."
  },
  {
    "text": "show unstable tests in last run",
    "intent": "flaky_tests",
    "response": "I can show you tests that appear to be unstable or flaky."
  },
  {
    "text": "show unstable tests from test results",
    "intent": "flaky_tests",
    "response": "I can show you tests that appear to be unstable or flaky."
  },
  {
    "text": "show unstable tests in current suite",
    "intent": "flaky_tests",
    "response": "I can show you tests that appear to be unstable or flaky."
  },
  {
    "text": "find intermittent failures",
    "intent": "flaky_tests",
    "response": "I can help identify tests with intermittent failure patterns."
  },
  {
    "text": "how many find intermittent failures",
    "intent": "flaky_tests",
    "response": "I can help identify tests with intermittent failure patterns."
  },
  {
    "text": "what are the find intermittent failures",
    "intent": "flaky_tests",
    "response": "I can help identify tests with intermittent failure patterns."
  },
  {
    "text": "show me find intermittent failures",
    "intent": "flaky_tests",
    "response": "I can help identify tests with intermittent failure patterns."
  },
  {
    "text": "which tests are unreliable",
    "intent": "flaky_tests",
    "response": "I can analyze test history to find unreliable tests."
  },
  {
    "text": "which tests are unreliable in last run",
    "intent": "flaky_tests",
    "response": "I can analyze test history to find unreliable tests."
  },
  {
    "text": "which tests are unreliable from test results",
    "intent": "flaky_tests",
    "response": "I can analyze test history to find unreliable tests."
  },
  {
    "text": "which tests are unreliable in current suite",
    "intent": "flaky_tests",
    "response": "I can analyze test history to find unreliable tests."
  },
  {
    "text": "api testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many api testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the api testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me api testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "test API endpoints",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many test API endpoints",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the test API endpoints",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me test API endpoints",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how do I test the API",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how do I test the API in last run",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how do I test the API from test results",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how do I test the API in current suite",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API test results",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API test results in last run",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API test results from test results",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API test results in current suite",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "API test cases",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many API test cases",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the API test cases",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API test cases",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "API automation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many API automation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the API automation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API automation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "API validation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many API validation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the API validation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API validation",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "API response testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "how many API response testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "what are the API response testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "show me API response testing",
    "intent": "api_testing",
    "response": "This involves API testing. I can help you with API test cases and results."
  },
  {
    "text": "list all failed tests",
    "intent": "failed_tests_list",
    "response": "I can provide a list of all failed tests from your test results."
  },
  {
    "text": "list all failed tests in last run",
    "intent": "failed_tests_list",
    "response": "I can provide a list of all failed tests from your test results."
  },
  {
    "text": "list all failed tests from test results",
    "intent": "failed_tests_list",
    "response": "I can provide a list of all failed tests from your test results."
  },
  {
    "text": "list all failed tests in current suite",
    "intent": "failed_tests_list",
    "response": "I can provide a list of all failed tests from your test results."
  },
  {
    "text": "show failed test cases",
    "intent": "failed_tests_list",
    "response": "I can show you all the test cases that failed in the last run."
  },
  {
    "text": "show failed test cases in last run",
    "intent": "failed_tests_list",
    "response": "I can show you all the test cases that failed in the last run."
  },
  {
    "text": "show failed test cases from test results",
    "intent": "failed_tests_list",
    "response": "I can show you all the test cases that failed in the last run."
  },
  {
    "text": "show failed test cases in current suite",
    "intent": "failed_tests_list",
    "response": "I can show you all the test cases that failed in the last run."
  },
  {
    "text": "what tests failed",
    "intent": "failed_tests_list",
    "response": "I can tell you which tests failed and provide details."
  },
  {
    "text": "what tests failed in last run",
    "intent": "failed_tests_list",
    "response": "I can tell you which tests failed and provide details."
  },
  {
    "text": "what tests failed from test results",
    "intent": "failed_tests_list",
    "response": "I can tell you which tests failed and provide details."
  },
  {
    "text": "what tests failed in current suite",
    "intent": "failed_tests_list",
    "response": "I can tell you which tests failed and provide details."
  },
  {
    "text": "failed test details",
    "intent": "failed_test_details",
    "response": "I can provide detailed information about failed tests including error messages."
  },
  {
    "text": "how many failed test details",
    "intent": "failed_test_details",
    "response": "I can provide detailed information about failed tests including error messages."
  },
  {
    "text": "what are the failed test details",
    "intent": "failed_test_details",
    "response": "I can provide detailed information about failed tests including error messages."
  },
  {
    "text": "show me failed test details",
    "intent": "failed_test_details",
    "response": "I can provide detailed information about failed tests including error messages."
  },
  {
    "text": "why did tests fail",
    "intent": "failed_test_analysis",
    "response": "I can analyze test failures and help identify potential causes."
  },
  {
    "text": "how many why did tests fail",
    "intent": "failed_test_analysis",
    "response": "I can analyze test failures and help identify potential causes."
  },
  {
    "text": "what are the why did tests fail",
    "intent": "failed_test_analysis",
    "response": "I can analyze test failures and help identify potential causes."
  },
  {
    "text": "show me why did tests fail",
    "intent": "failed_test_analysis",
    "response": "I can analyze test failures and help identify potential causes."
  },
  {
    "text": "test failure reasons",
    "intent": "failed_test_analysis",
    "response": "I can help analyze the reasons behind test failures."
  },
  {
    "text": "how many test failure reasons",
    "intent": "failed_test_analysis",
    "response": "I can help analyze the reasons behind test failures."
  },
  {
    "text": "what are the test failure reasons",
    "intent": "failed_test_analysis",
    "response": "I can help analyze the reasons behind test failures."
  },
  {
    "text": "show me test failure reasons",
    "intent": "failed_test_analysis",
    "response": "I can help analyze the reasons behind test failures."
  },
  {
    "text": "slowest tests",
    "intent": "test_performance",
    "response": "I can identify the slowest running tests in your suite."
  },
  {
    "text": "how many slowest tests",
    "intent": "test_performance",
    "response": "I can identify the slowest running tests in your suite."
  },
  {
    "text": "what are the slowest tests",
    "intent": "test_performance",
    "response": "I can identify the slowest running tests in your suite."
  },
  {
    "text": "show me slowest tests",
    "intent": "test_performance",
    "response": "I can identify the slowest running tests in your suite."
  },
  {
    "text": "test execution time",
    "intent": "test_performance",
    "response": "I can analyze test execution times and performance."
  },
  {
    "text": "how many test execution time",
    "intent": "test_performance",
    "response": "I can analyze test execution times and performance."
  },
  {
    "text": "what are the test execution time",
    "intent": "test_performance",
    "response": "I can analyze test execution times and performance."
  },
  {
    "text": "show me test execution time",
    "intent": "test_performance",
    "response": "I can analyze test execution times and performance."
  },
  {
    "text": "which tests take longest",
    "intent": "test_performance",
    "response": "I can show you which tests take the longest to execute."
  },
  {
    "text": "which tests take longest in last run",
    "intent": "test_performance",
    "response": "I can show you which tests take the longest to execute."
  },
  {
    "text": "which tests take longest from test results",
    "intent": "test_performance",
    "response": "I can show you which tests take the longest to execute."
  },
  {
    "text": "which tests take longest in current suite",
    "intent": "test_performance",
    "response": "I can show you which tests take the longest to execute."
  },
  {
    "text": "test timing analysis",
    "intent": "test_performance",
    "response": "I can provide analysis of test timing and performance metrics."
  },
  {
    "text": "how many test timing analysis",
    "intent": "test_performance",
    "response": "I can provide analysis of test timing and performance metrics."
  },
  {
    "text": "what are the test timing analysis",
    "intent": "test_performance",
    "response": "I can provide analysis of test timing and performance metrics."
  },
  {
    "text": "show me test timing analysis",
    "intent": "test_performance",
    "response": "I can provide analysis of test timing and performance metrics."
  },
  {
    "text": "total duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many total duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the total duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me total duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "total duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many total duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the total duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me total duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me duration?",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how long did the test suite take",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how long did the test suite take in last run",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how long did the test suite take from test results",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how long did the test suite take in current suite",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "test run duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many test run duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the test run duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me test run duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "overall test duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "how many overall test duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "what are the overall test duration",
    "intent": "test_duration",
    "response": "I can show you the total duration of your test run."
  },
  {
    "text": "show me ov
...