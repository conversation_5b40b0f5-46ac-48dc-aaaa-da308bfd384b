!/usr/bin/env python3
"""
Train a Naive <PERSON> model on <PERSON>wright test data for intent classification.
Includes data extraction, training, and model management.
"""

import json
import joblib
import os
import re
from collections import defaultdict
from typing import Dict, List, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB  
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import numpy as np

# Data extraction functions
def extract_test_features(title: str, file_path: str) -> List[str]:
    """Extract meaningful features from test titles and file paths."""
    features = []
    
    # Clean and split title
    title_words = re.sub(r'[^\w\s]', ' ', title.lower()).split()
    features.extend(title_words)
    
    # Extract file path components
    file_parts = file_path.replace('\\', '/').split('/')
    for part in file_parts:
        if part.endswith('.spec.ts'):
            part = part.replace('.spec.ts', '')
        features.extend(part.replace('-', ' ').replace('_', ' ').split())
    
    # Remove common stop words and very short words
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'can', 'is', 'are', 'a', 'an'}
    features = [f for f in features if f not in stop_words and len(f) > 2]
    
    return features

def categorize_intent(title: str, file_path: str, status: str) -> str:
    """Categorize test into intents based on title, file path, and status."""
    title_lower = title.lower()
    file_lower = file_path.lower()
    
    # Test execution status intents (keep these for individual test analysis)
    if status in ['failed', 'timedOut']:
        return 'test_failure'
    elif status == 'passed':
        return 'test_success'
    elif status == 'skipped':
        return 'test_skipped'
    
    # Feature-based intents
    if 'sprint_planning' in file_lower:
        return 'sprint_planning'
    elif 'sprint_retrospective' in file_lower:
        return 'sprint_retrospective'
    elif 'sprint_execution' in file_lower or 'board' in file_lower:
        return 'sprint_execution'
    elif 'asset_details' in file_lower or 'asset-details' in file_lower:
        return 'asset_details'
    elif 'user_dashboard' in file_lower or 'dashboard' in title_lower:
        return 'user_dashboard'
    elif 'api' in title_lower or 'api' in file_lower:
        return 'api_testing'
    elif 'live_updates' in file_lower or 'live-updates' in file_lower:
        return 'live_updates'
    elif 'standup' in title_lower or 'standup' in file_lower:
        return 'standup'
    elif 'configuration' in title_lower or 'config' in title_lower:
        return 'configuration'
    elif 'filter' in title_lower or 'search' in title_lower:
        return 'filtering'
    elif 'permission' in title_lower or 'access' in title_lower:
        return 'permissions'
    elif 'error' in title_lower or 'notification' in title_lower:
        return 'error_handling'
    elif 'kanban' in title_lower:
        return 'kanban'
    elif 'swimlane' in title_lower:
        return 'swimlanes'
    elif 'field' in title_lower or 'richtext' in title_lower:
        return 'form_fields'
    else:
        return 'general_testing'

def generate_analytical_training_data() -> List[dict]:
    # Extra explicit/short examples for test_duration intent
    duration_variations = [
        "duration?", "test duration?", "suite duration?", "how long?", "how long did it take?", "how long did tests take?", "how long did the suite take?", "show duration", "show test duration", "show suite duration", "show me duration", "show me test duration", "show me suite duration", "display duration", "display test duration", "display suite duration", "print duration", "print test duration", "print suite duration", "total run time", "total run time?", "run duration", "run duration?", "execution duration", "execution duration?", "test execution duration", "test execution duration?", "suite execution duration", "suite execution duration?", "overall duration", "overall duration?", "overall test duration?", "overall suite duration?"
    ]
    for phrase in duration_variations:
        performance_questions.append((phrase, "test_duration", "I can show you the total duration of your test run."))
    """Generate training data for analytical questions about test results."""
    analytical_data = []
    
    # Test count and statistics questions
    count_questions = [
        ("how many tests passed", "test_count_passed", "I can analyze your test results to count passed tests."),
        ("how many tests failed", "test_count_failed", "I can analyze your test results to count failed tests."),
        ("how many tests were skipped", "test_count_skipped", "I can analyze your test results to count skipped tests."),
        ("total number of tests", "test_count_total", "I can count the total number of tests in your test suite."),
        ("test execution summary", "test_summary", "I can provide a complete summary of your test execution results."),
        ("pass rate", "test_pass_rate", "I can calculate the test pass rate from your results."),
        ("test statistics", "test_statistics", "I can provide detailed statistics about your test execution."),
        ("overall test status", "test_overview", "I can give you an overview of your overall test execution status."),
        ("test overview", "test_overview", "I can give you an overview of your overall test execution status."),
        ("give me a test overview", "test_overview", "I can give you an overview of your overall test execution status."),
        ("show me a test overview", "test_overview", "I can give you an overview of your overall test execution status."),
        ("can you provide a test overview", "test_overview", "I can give you an overview of your overall test execution status."),
        ("summary of all test results", "test_overview", "I can give you an overview of your overall test execution status."),
        ("overall test results", "test_overview", "I can give you an overview of your overall test execution status."),
        ("overall test execution", "test_overview", "I can give you an overview of your overall test execution status."),
    ]
    
    # Flaky tests questions
    flaky_questions = [
        ("list flaky tests", "flaky_tests", "I can identify potentially flaky tests that have inconsistent results."),
        ("show unstable tests", "flaky_tests", "I can show you tests that appear to be unstable or flaky."),
        ("find intermittent failures", "flaky_tests", "I can help identify tests with intermittent failure patterns."),
        ("which tests are unreliable", "flaky_tests", "I can analyze test history to find unreliable tests."),
    ]

    # API testing questions (improve intent recognition)
    api_testing_questions = [
        ("api testing", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("test API endpoints", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("how do I test the API", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("show me API test results", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("API test cases", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("API automation", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("API validation", "api_testing", "This involves API testing. I can help you with API test cases and results."),
        ("API response testing", "api_testing", "This involves API testing. I can help you with API test cases and results."),
    ]
    
    # Failed tests questions
    failed_questions = [
        ("list all failed tests", "failed_tests_list", "I can provide a list of all failed tests from your test results."),
        ("show failed test cases", "failed_tests_list", "I can show you all the test cases that failed in the last run."),
        ("what tests failed", "failed_tests_list", "I can tell you which tests failed and provide details."),
        ("failed test details", "failed_test_details", "I can provide detailed information about failed tests including error messages."),
        ("why did tests fail", "failed_test_analysis", "I can analyze test failures and help identify potential causes."),
        ("test failure reasons", "failed_test_analysis", "I can help analyze the reasons behind test failures."),
    ]
    
    # Test duration and performance questions
    performance_questions = [
        ("slowest tests", "test_performance", "I can identify the slowest running tests in your suite."),
        ("test execution time", "test_performance", "I can analyze test execution times and performance."),
        ("which tests take longest", "test_performance", "I can show you which tests take the longest to execute."),
        ("test timing analysis", "test_performance", "I can provide analysis of test timing and performance metrics."),
        ("total duration", "test_duration", "I can show you the total duration of your test run."),
        ("total duration?", "test_duration", "I can show you the total duration of your test run."),
        ("duration", "test_duration", "I can show you the total duration of your test run."),
        ("duration?", "test_duration", "I can show you the total duration of your test run."),
        ("how long did the test suite take", "test_duration", "I can show you the total duration of your test run."),
        ("test run duration", "test_duration", "I can show you the total duration of your test run."),
        ("overall test duration", "test_duration", "I can show you the total duration of your test run."),
        ("suite duration", "test_duration", "I can show you the total duration of your test run."),
        ("test duration", "test_duration", "I can show you the total duration of your test run."),
        ("run time", "test_duration", "I can show you the total duration of your test run."),
        ("run time?", "test_duration", "I can show you the total duration of your test run."),
        ("test suite run time", "test_duration", "I can show you the total duration of your test run."),
        ("how long did it take to run the tests", "test_duration", "I can show you the total duration of your test run."),
        ("how long did the tests take", "test_duration", "I can show you the total duration of your test run."),
        ("how long did it take?", "test_duration", "I can show you the total duration of your test run."),
    ]
    
    # Test suite organization questions
    organization_questions = [
        ("tests by feature", "test_organization", "I can organize and group tests by feature or functionality."),
        ("test coverage by area", "test_coverage", "I can show test coverage across different application areas."),
        ("list tests by module", "test_organization", "I can list tests organized by module or component."),
        ("test distribution", "test_distribution", "I can show how tests are distributed across different areas."),
    ]
    
    # Specific test queries
    specific_questions = [
        ("find test by name", "test_search", "I can help you find specific tests by name or partial matches."),
        ("search for tests", "test_search", "I can search through your test suite to find specific tests."),
        ("test details", "test_details", "I can provide detailed information about specific tests."),
        ("test history", "test_history", "I can show the execution history of specific tests."),
        ("when were tests last run", "test_history", "Tests were last executed on [timestamp from test results]."),
        ("when did tests run", "test_history", "Test execution timestamp information."),
        ("test execution time", "test_history", "Information about when tests were executed."),
        ("last test run", "test_history", "Details about the most recent test execution."),
        ("test run timestamp", "test_history", "Timestamp of the latest test execution."),
        ("when was the test suite executed", "test_history", "Test suite execution timing information."),
    ]
    
    # Combine all question types
    all_questions = (count_questions + flaky_questions + api_testing_questions + failed_questions + 
                    performance_questions + organization_questions + specific_questions)
    
    # Generate training examples
    for question, intent, response in all_questions:
        analytical_data.append({
            'text': question,
            'intent': intent,
            'response': response
        })
        
        # Add variations of each question
        variations = generate_question_variations(question)
        for variation in variations:
            analytical_data.append({
                'text': variation,
                'intent': intent,
                'response': response
            })
    
    return analytical_data

def generate_question_variations(base_question: str) -> List[str]:
    """Generate variations of questions to improve training diversity."""
    variations = []
    
    # Add question words variations
    if not any(base_question.startswith(qw) for qw in ['how', 'what', 'which', 'show', 'list']):
        variations.append(f"how many {base_question}")
        variations.append(f"what are the {base_question}")
        variations.append(f"show me {base_question}")
        variations.append(f"list {base_question}")
    
    # Add context variations
    variations.extend([
        f"{base_question} in last run",
        f"{base_question} from test results",
        f"{base_question} in current suite",
        f"can you {base_question}",
        f"please {base_question}",
    ])
    
    return variations[:3]  # Limit to 3 variations per question

def generate_response(intent: str, title: str, status: str, file_path: str) -> str:
    """Generate appropriate response based on intent and test details."""
    responses = {
        # Individual test responses
        'test_failure': f"Test '{title}' failed. Check the test execution logs and fix the failing assertions.",
        'test_success': f"Test '{title}' passed successfully. The feature is working as expected.",
        'test_skipped': f"Test '{title}' was skipped. This test may be disabled or conditionally skipped.",
        
        # Feature-specific responses
        'sprint_planning': f"This relates to sprint planning functionality. Test: '{title}' in {file_path}",
        'sprint_retrospective': f"This is about sprint retrospective features. Test: '{title}' covers retrospective functionality.",
        'sprint_execution': f"This concerns sprint execution and board functionality. Test: '{title}' validates execution features.",
        'asset_details': f"This is related to asset details functionality. Test: '{title}' verifies asset detail features.",
        'user_dashboard': f"This pertains to user dashboard features. Test: '{title}' checks dashboard functionality.",
        'api_testing': f"This involves API testing. Test: '{title}' validates API behavior and responses.",
        'live_updates': f"This is about live updates functionality. Test: '{title}' ensures real-time updates work correctly.",
        'standup': f"This relates to standup tool features. Test: '{title}' validates standup functionality.",
        'configuration': f"This involves configuration settings. Test: '{title}' checks configuration features.",
        'filtering': f"This is about filtering and search functionality. Test: '{title}' validates filtering features.",
        'permissions': f"This relates to permissions and access control. Test: '{title}' checks permission settings.",
        'error_handling': f"This involves error handling and notifications. Test: '{title}' validates error scenarios.",
        'kanban': f"This is about Kanban board functionality. Test: '{title}' tests Kanban features.",
        'swimlanes': f"This relates to swimlane functionality. Test: '{title}' validates swimlane features.",
        'form_fields': f"This involves form fields and input handling. Test: '{title}' tests field functionality.",
        'general_testing': f"This is a general test case. Test: '{title}' validates application functionality.",
        
        # Analytical responses (these will be overridden by actual analysis in the chat system)
        'test_count_passed': "Let me analyze your test results to count the passed tests.",
        'test_count_failed': "Let me analyze your test results to count the failed tests.",
        'test_count_skipped': "Let me analyze your test results to count the skipped tests.",
        'test_count_total': "Let me count the total number of tests in your test suite.",
        'test_summary': "Let me provide a complete summary of your test execution results.",
        'test_pass_rate': "Let me calculate the test pass rate from your results.",
        'test_statistics': "Let me provide detailed statistics about your test execution.",
        'test_overview': "Let me give you an overview of your test execution status.",
        'flaky_tests': "Let me identify potentially flaky or unstable tests in your suite.",
        'failed_tests_list': "Let me provide a list of all failed tests from your results.",
        'failed_test_details': "Let me provide detailed information about failed tests including error messages.",
        'failed_test_analysis': "Let me analyze test failures and help identify potential causes.",
        'test_performance': "Let me analyze test execution times and identify performance issues.",
        'test_organization': "Let me organize and group your tests by feature or functionality.",
        'test_coverage': "Let me show test coverage across different application areas.",
        'test_distribution': "Let me show how tests are distributed across different areas.",
        'test_search': "Let me help you find specific tests by name or partial matches.",
        'test_details': "Let me provide detailed information about specific tests.",
        'test_history': "Let me show the execution history of specific tests.",
    }
    
    return responses.get(intent, f"Test: '{title}' - Status: {status}")

def extract_training_data_from_json(json_file_path: str) -> List[dict]:
    """Extract training data from Playwright test results JSON."""
    
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    simplified_data = []
    
    # Process each suite for individual test analysis
    for suite in data.get('suites', []):
        file_path = suite.get('file', '')
        
        # Process each spec in the suite
        for spec in suite.get('specs', []):
            title = spec.get('title', '')
            
            # Process each test in the spec
            for test in spec.get('tests', []):
                status = test.get('status', 'unknown')
                
                # Extract features for intent classification
                features = extract_test_features(title, file_path)
                intent = categorize_intent(title, file_path, status)
                
                # Create training example
                training_text = ' '.join(features)
                response_text = generate_response(intent, title, status, file_path)
                
                simplified_data.append({
                    'text': training_text,
                    'intent': intent,
                    'response': response_text
                })
    
    # Add analytical training data for systematic questions
    analytical_data = generate_analytical_training_data()
    simplified_data.extend(analytical_data)
    
    print(f"Generated {len(simplified_data)} total training examples:")
    print(f"  - Individual test examples: {len(simplified_data) - len(analytical_data)}")
    print(f"  - Analytical question examples: {len(analytical_data)}")
    
    return simplified_data

class TestIntentClassifier:
    def __init__(self):
        self.pipeline = Pipeline([
            ('tfidf', TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 2),
                stop_words='english',
                lowercase=True,
                strip_accents='ascii'
            )),
            ('classifier', MultinomialNB(alpha=0.1))
        ])
        self.intent_responses = {}
        self.model_path = 'test_intent_classifier.pkl'
        self.test_data = None  # Store parsed test data for analysis
    
    def load_test_data(self, test_results_path: str):
        """Load and parse test results for analysis."""
        with open(test_results_path, 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)
    
    def analyze_test_results(self, intent: str) -> str:
        """Perform actual analysis of test results based on intent."""
        if not self.test_data:
            return "Test data not loaded. Please ensure test results are available."
        
        # Use the stats section (correct way!)
        stats = self.test_data.get('stats', {})
        passed_count = stats.get('expected', 0)    # expected = passed
        failed_count = stats.get('unexpected', 0)  # unexpected = failed  
        skipped_count = stats.get('skipped', 0)    # skipped = skipped
        total_count = passed_count + failed_count + skipped_count
        
    def analyze_test_results(self, intent: str) -> str:
        """Perform actual analysis of test results based on intent."""
        if not self.test_data:
            return "Test data not loaded. Please ensure test results are available."
        
        # Use the stats section (correct way!)
        stats = self.test_data.get('stats', {})
        passed_count = stats.get('expected', 0)    # expected = passed
        failed_count = stats.get('unexpected', 0)  # unexpected = failed  
        skipped_count = stats.get('skipped', 0)    # skipped = skipped
        total_count = passed_count + failed_count + skipped_count
        
        # Get failed tests details if needed
        failed_tests = []
        if failed_count > 0:
            for suite in self.test_data.get('suites', []):
                for spec in suite.get('specs', []):
                    for test in spec.get('tests', []):
                        # Check the actual result status (in results array)
                        results = test.get('results', [])
                        if results and len(results) > 0:
                            status = results[0].get('status', 'unknown')
                            if status in ['failed', 'timedOut']:
                                failed_tests.append({
                                    'title': spec.get('title', ''),
                                    'file': suite.get('file', ''),
                                    'status': status,
                                    'error': results[0].get('error', {}).get('message', 'No error message')
                                })
        
        # Generate responses based on intent
        if intent == 'test_count_passed':
            return f"✅ **{passed_count}** tests passed out of {total_count} total tests."
        
        elif intent == 'test_count_failed':
            return f"❌ **{failed_count}** tests failed out of {total_count} total tests."
        
        elif intent == 'test_count_skipped':
            return f"⏭️ **{skipped_count}** tests were skipped out of {total_count} total tests."
        
        elif intent == 'test_count_total':
            return f"📊 **Total: {total_count}** tests in the suite."
        
        elif intent == 'test_summary':
            pass_rate = (passed_count / total_count * 100) if total_count > 0 else 0
            return f"""📊 **Test Execution Summary:**
• Total Tests: **{total_count}**
• ✅ Passed: **{passed_count}** ({pass_rate:.1f}%)
• ❌ Failed: **{failed_count}**
• ⏭️ Skipped: **{skipped_count}**
• Pass Rate: **{pass_rate:.1f}%**"""
        
        elif intent == 'test_pass_rate':
            pass_rate = (passed_count / total_count * 100) if total_count > 0 else 0
            return f"📈 **Test Pass Rate: {pass_rate:.1f}%** ({passed_count}/{total_count} tests passed)"

        elif intent == 'test_duration':
            duration_ms = stats.get('duration', 0)
            duration_sec = duration_ms / 1000 if duration_ms else 0
            return f"⏱️ **Total Test Duration:** {duration_sec:.1f} seconds ({duration_ms:,.0f} ms)"

        elif intent == 'failed_tests_list':
            if failed_count == 0:
                return "🎉 **No failed tests!** All tests either passed or were skipped."
            
            # Use stats count since individual test parsing might not work perfectly
            result = f"❌ **{failed_count} tests failed** out of {total_count} total tests.\n\n"
            
            # Try to get detailed failed tests, but don't depend on it
            if failed_tests:
                result += "**Failed test details:**\n"
                for i, test in enumerate(failed_tests[:10], 1):  # Limit to first 10
                    result += f"{i}. **{test['title']}** (in `{test['file']}`) - Status: {test['status']}\n"
                
                if len(failed_tests) > 10:
                    result += f"\n... and {len(failed_tests) - 10} more failed tests."
            else:
                result += f"*Note: Failed test details not available, but stats show {failed_count} failures.*"
            
            return result
            
            return result
        
        elif intent == 'test_history':
            # Get timestamp from stats section
            stats = self.test_data.get('stats', {})
            start_time = stats.get('startTime', 'Unknown')
            
            if start_time != 'Unknown':
                # Parse and format the timestamp
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    formatted_time = dt.strftime('%Y-%m-%d at %H:%M:%S UTC')
                    duration_ms = stats.get('duration', 0)
                    duration_sec = duration_ms / 1000 if duration_ms else 0
                    
                    return f"""⏰ **Test Execution History:**
• **Last Run:** {formatted_time}
• **Duration:** {duration_sec:.1f} seconds ({duration_ms:,.0f}ms)
• **Total Tests:** {total_count}
• **Results:** {passed_count} passed, {failed_count} failed, {skipped_count} skipped"""
                except Exception:
                    return f"⏰ **Tests were last run on:** {start_time}"
            else:
                return "❌ Test execution timestamp not available in results."
        
        elif intent == 'flaky_tests':
            # Try to find flaky tests in the data (if any)
            flaky_tests = []
            for suite in self.test_data.get('suites', []):
                for spec in suite.get('specs', []):
                    for test in spec.get('tests', []):
                        # A flaky test is one that has both passed and failed results
                        statuses = set()
                        for result in test.get('results', []):
                            statuses.add(result.get('status', 'unknown'))
                        if 'passed' in statuses and ('failed' in statuses or 'timedOut' in statuses):
                            flaky_tests.append({
                                'title': spec.get('title', ''),
                                'file': suite.get('file', ''),
                                'statuses': list(statuses)
                            })
            if not flaky_tests:
                return "🎉 No flaky tests found in your test results. All tests are stable."
            else:
                result = f"🔄 **Flaky Test Analysis:** Found {len(flaky_tests)} potentially flaky test(s):\n\n"
                for i, test in enumerate(flaky_tests[:10], 1):
                    result += f"{i}. **{test['title']}** (in `{test['file']}`) - Statuses: {', '.join(test['statuses'])}\n"
                if len(flaky_tests) > 10:
                    result += f"\n... and {len(flaky_tests) - 10} more flaky tests."
                return result
        
        elif intent in ['test_statistics', 'test_overview']:
            return self.analyze_test_results('test_summary')
        
        elif intent == 'test_details':
            # Try to extract a test name from the user input (very basic: look for quoted or last word after 'for')
            import re
            # Use the last user input (if available)
            user_input = getattr(self, '_last_user_input', None)
            test_name = None
            if user_input:
                # Try to extract quoted string
                match = re.search(r'"([^"]+)"