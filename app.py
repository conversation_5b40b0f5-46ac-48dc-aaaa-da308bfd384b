#!/usr/bin/env python3
"""
Gradio interface for the Test Intent Classifier.
Main application to interact with the trained Naive <PERSON> model.
"""

import gradio as gr
import os
import sys
from train_model import TestIntentClassifier, train_model
import json

class TestChatBot:
    def __init__(self):
        self.classifier = TestIntentClassifier()
        self.load_or_train_model()
    
    def load_or_train_model(self):
        """Load existing model or train a new one."""
        try:
            if self.classifier.is_model_trained():
                self.classifier.load_model()
                print("✅ Model loaded successfully!")
            else:
                print("🔄 No trained model found. Training new model...")
                self.classifier = train_model()
                print("✅ Model trained and ready!")
        except Exception as e:
            print(f"❌ Error loading/training model: {e}")
            raise e
    
    def chat(self, message, history):
        """Process chat message and return response."""
        if not message.strip():
            return history, ""
        
        try:
            # Get response from classifier
            result = self.classifier.chat_response(message)
            
            # Format the response
            response = f"**Intent:** {result['intent'].replace('_', ' ').title()}\n"
            response += f"**Confidence:** {result['confidence']:.2%}\n\n"
            response += f"**Response:** {result['response']}"
            
            # Add to history in messages format
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": response})
            
        except Exception as e:
            error_response = f"❌ Error processing your request: {str(e)}"
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": error_response})
        
        return history, ""
    
    def get_intent_info(self):
        """Get information about available intents."""
        if hasattr(self.classifier, 'intent_responses'):
            intents = list(self.classifier.intent_responses.keys())
            intent_info = "## Available Intents:\n\n"
            for i, intent in enumerate(sorted(intents), 1):
                intent_info += f"{i}. **{intent.replace('_', ' ').title()}**\n"
            return intent_info
        return "Model not loaded properly."

def create_interface():
    """Create the Gradio interface."""
    
    # Initialize chatbot
    try:
        chatbot = TestChatBot()
    except Exception as e:
        print(f"Failed to initialize chatbot: {e}")
        return None
    
    # Custom CSS
    css = """
    .gradio-container {
        max-width: 800px !important;
        margin: auto !important;
    }
    .chatbot {
        height: 500px !important;
    }
    """
    
    with gr.Blocks(css=css, title="Test Intent Classifier", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 Test Intent Classifier
        
        This AI assistant can help you understand test automation queries and provide relevant responses 
        based on Playwright test results from the Agility Rooms project.
        
        **Try asking about:**
        - Test failures or successes
        - Sprint planning, execution, or retrospective features
        - Asset details, user dashboard, or API functionality
        - Kanban boards, swimlanes, or configuration settings
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot_interface = gr.Chatbot(
                    label="Chat with Test Assistant",
                    height=500,
                    type="messages"
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        placeholder="Ask me about test automation, features, or specific test cases...",
                        label="Your message",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("Send", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("Clear Chat", variant="secondary")
                    
            with gr.Column(scale=1):
                intent_info = gr.Markdown(
                    chatbot.get_intent_info(),
                    label="Available Intents"
                )
        
        # Example queries
        gr.Markdown("""
        ## 💡 Example Queries:
        - "Why did my sprint planning test fail?"
        - "Tell me about asset details functionality"
        - "How do I test kanban board features?"
        - "What are the API testing capabilities?"
        - "Sprint execution board is not working"
        """)
        
        # Event handlers
        def submit_message(message, history):
            return chatbot.chat(message, history)
        
        def clear_chat():
            return [], ""
        
        # Bind events
        msg.submit(submit_message, [msg, chatbot_interface], [chatbot_interface, msg])
        send_btn.click(submit_message, [msg, chatbot_interface], [chatbot_interface, msg])
        clear_btn.click(clear_chat, [], [chatbot_interface, msg])
        
        # Add some statistics
        with gr.Accordion("📊 Model Statistics", open=False):
            if hasattr(chatbot.classifier, 'intent_responses'):
                total_intents = len(chatbot.classifier.intent_responses)
                total_responses = sum(len(responses) for responses in chatbot.classifier.intent_responses.values())
                
                gr.Markdown(f"""
                **Model Information:**
                - Total Intents: {total_intents}
                - Total Training Examples: {total_responses}
                - Model Type: Multinomial Naive Bayes
                - Feature Extraction: TF-IDF Vectorization
                """)
    
    return demo

def main():
    """Launch the Gradio application."""
    print("🚀 Starting Test Intent Classifier...")
    
    # Create interface
    demo = create_interface()
    
    if demo is None:
        print("❌ Failed to create interface")
        return
    
    # Launch the app
    try:

        demo.launch(
            server_name="127.0.0.1",  # Use localhost instead of 0.0.0.0
            server_port=7860,
            share=True,
            show_error=True,
            quiet=False

        )
    except Exception as e:
        print(f"❌ Error launching app: {e}")
with open("test-results.json", "r", encoding="utf-8") as f:
    data = json.load(f)
print("Loaded keys:", list(data.keys()))
if __name__ == "__main__":
    main()